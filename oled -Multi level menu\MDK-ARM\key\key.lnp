--cpu Cortex-M3
"key\startup_stm32f103xe.o"
"key\main.o"
"key\gpio.o"
"key\dma.o"
"key\i2c.o"
"key\usart.o"
"key\stm32f1xx_it.o"
"key\stm32f1xx_hal_msp.o"
"key\stm32f1xx_hal_gpio_ex.o"
"key\stm32f1xx_hal_i2c.o"
"key\stm32f1xx_hal.o"
"key\stm32f1xx_hal_rcc.o"
"key\stm32f1xx_hal_rcc_ex.o"
"key\stm32f1xx_hal_gpio.o"
"key\stm32f1xx_hal_dma.o"
"key\stm32f1xx_hal_cortex.o"
"key\stm32f1xx_hal_pwr.o"
"key\stm32f1xx_hal_flash.o"
"key\stm32f1xx_hal_flash_ex.o"
"key\stm32f1xx_hal_exti.o"
"key\stm32f1xx_hal_uart.o"
"key\system_stm32f1xx.o"
"key\key_app.o"
"key\led_app.o"
"key\scheduler.o"
"key\btn_app.o"
"key\usart_app.o"
"key\oled_app.o"
"key\ebtn.o"
"key\ringbuffer.o"
"key\oled.o"
"key\mui.o"
"key\mui_u8g2.o"
"key\u8g2_arc.o"
"key\u8g2_bitmap.o"
"key\u8g2_box.o"
"key\u8g2_buffer.o"
"key\u8g2_button.o"
"key\u8g2_circle.o"
"key\u8g2_cleardisplay.o"
"key\u8g2_d_memory.o"
"key\u8g2_d_setup.o"
"key\u8g2_font.o"
"key\u8g2_fonts.o"
"key\u8g2_hvline.o"
"key\u8g2_input_value.o"
"key\u8g2_intersection.o"
"key\u8g2_kerning.o"
"key\u8g2_line.o"
"key\u8g2_ll_hvline.o"
"key\u8g2_message.o"
"key\u8g2_polygon.o"
"key\u8g2_selection_list.o"
"key\u8g2_setup.o"
"key\u8log.o"
"key\u8log_u8g2.o"
"key\u8log_u8x8.o"
"key\u8x8_8x8.o"
"key\u8x8_byte.o"
"key\u8x8_cad.o"
"key\u8x8_capture.o"
"key\u8x8_d_ssd1306_128x64_noname.o"
"key\u8x8_debounce.o"
"key\u8x8_display.o"
"key\u8x8_fonts.o"
"key\u8x8_gpio.o"
"key\u8x8_input_value.o"
"key\u8x8_message.o"
"key\u8x8_selection_list.o"
"key\u8x8_setup.o"
"key\u8x8_string.o"
"key\u8x8_u8toa.o"
"key\u8x8_u16toa.o"
--library_type=microlib --strict --scatter "key\key.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "key.map" -o key\key.axf